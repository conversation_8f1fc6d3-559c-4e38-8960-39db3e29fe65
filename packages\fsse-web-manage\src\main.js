import { createApp, createVNode } from 'vue';
import { ExclamationCircleFilled } from '@ant-design/icons-vue';
import { Modal } from 'ant-design-vue';
import App from './App.vue';
import pinia from '@/store/pinia';
import 'uno.css';
import '../../../styles/common.less';
import '../../../styles/antd.less';
import yideUiVue from 'yide-ui-vue';
const app = createApp(App);
directive(app);
app.use(pinia);

const init = async () => {
  import('@/router/index.js').then(async res => {
    const router = await res.default();
    app.use(router).mount('#app');

    // 检查是否有保存的重定向URL
    const redirectUri = sessionStorage.getItem('login_redirect_uri');
    if (redirectUri) {
      sessionStorage.removeItem('login_redirect_uri');
      // 等待路由准备完成后再跳转
      router.isReady().then(() => {
        setTimeout(() => {
          window.location.href = redirectUri;
        }, 500);
      });
    }
  });
};
init();

app.use(yideUiVue, { http: http });

// 监听页面版本更新后 获取使用旧的js文件报错
window.errorFn = e => {
  if (window.$t) return false;
  if (e.target.tagName === 'LINK' || e.target.tagName === 'SCRIPT') {
    window.$t = true;
    Modal.confirm({
      title: '更新提示',
      icon: createVNode(ExclamationCircleFilled),
      content: '版本已更新，请确认更新页面',
      onOk() {
        window.location.reload();
      },
      onCancel() {
        window.location.reload();
      },
    });
  }
};
