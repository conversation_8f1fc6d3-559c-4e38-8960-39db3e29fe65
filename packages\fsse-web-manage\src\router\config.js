export default router => {
  router.beforeEach((to, from, next) => {
    const store = useStore();

    // 如果用户已经登录且试图访问登录页面
    if (to.path === '/login' && store.accessToken) {
      try {
        // 使用 getTargetUrlParams 函数获取 redirect_uri 参数
        const urlParams = getTargetUrlParams();
        const redirectUri = urlParams?.redirect_uri;

        if (redirectUri) {
          // 判断是否为外部链接
          if (redirectUri.startsWith('http://') || redirectUri.startsWith('https://')) {
            // 外部链接，使用 window.location.href 跳转
            window.location.href = redirectUri;
            return;
          } else {
            // 内部路由，使用 next() 跳转
            next(redirectUri);
            return;
          }
        } else {
          // 如果没有 redirect_uri 参数，重定向到首页
          next('/');
          return;
        }
      } catch (error) {
        console.error('获取URL参数失败:', error);
        // 如果获取参数失败，重定向到首页
        next('/');
        return;
      }
    }

    // 如果用户未登录且试图访问需要认证的页面，重定向到登录页
    if (to.path !== '/login' && !store.accessToken) {
      next('/login');
      return;
    }

    next();
  });
  router.afterEach(() => {
    setTimeout(() => {}, 300);
  });
};
