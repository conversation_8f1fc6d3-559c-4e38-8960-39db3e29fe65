export default router => {
  router.beforeEach((to, from, next) => {
    console.log('路由守卫 - to:', to.fullPath, 'from:', from.fullPath);
    const store = useStore();

    // 如果用户未登录且试图访问需要认证的页面，重定向到登录页
    if (to.path !== '/login' && !store.accessToken) {
      console.log('未登录用户访问认证页面，重定向到登录页');
      next('/login');
      return;
    }

    // 其他情况都允许通过（包括已登录用户访问登录页面）
    // 已登录用户访问登录页面的重定向逻辑在登录组件的 onMounted 中处理
    console.log('路由守卫通过');
    next();
  });
  router.afterEach(() => {
    setTimeout(() => {}, 300);
  });
};
