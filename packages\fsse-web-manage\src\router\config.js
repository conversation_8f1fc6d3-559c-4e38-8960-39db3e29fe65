export default router => {
  router.beforeEach((to, from, next) => {
    const store = useStore();

    // 如果用户已经登录且试图访问登录页面，重定向到首页
    if (to.path === '/login' && store.accessToken) {
      next('/');
      return;
    }

    // 如果用户未登录且试图访问需要认证的页面，重定向到登录页
    if (to.path !== '/login' && !store.accessToken) {
      next('/login');
      return;
    }

    next();
  });
  router.afterEach(() => {
    setTimeout(() => {}, 300);
  });
};
