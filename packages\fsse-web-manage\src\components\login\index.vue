<template>
  <div class="login_box">
    <div class="introduction">
      <img src="@/assets/images/bg.png" alt="logo" />
    </div>
    <div class="form_box">
      <!-- 登录 -->
      <LoginContent
        v-if="state.showArea === SHOW_AREA_MAP.LOGIN"
        :loading="state.loadingBtn"
        @submit="submitLogin"
        :loginText="state.loginText"
      />
      <!-- 重置密码 -->
      <Password
        v-else-if="state.showArea === SHOW_AREA_MAP.PASSWORD"
        @submit="submitLogin"
      />
    </div>
    <agencyActivate
      ref="agencyActivateRef"
      @confirm="confirmok"
      @cancel="cancelok"
    />
    <!-- <div class="copyright">
      版权所有©深圳市一德文化科技有限公司 粤ICP备17028019号
    </div> -->
  </div>
</template>

<script setup>
import LoginContent from './loginContent.vue';
import Password from './Password.vue';
import agencyActivate from './agencyActivate.vue';
import { getTargetUrlParams } from '../../../../utils/getTargetUrlParams.js';

import RSA from './rsa.js';
const agencyActivateRef = ref(null);
const store = useStore();

// 显示区域类型
const SHOW_AREA_MAP = {
  // 登录
  LOGIN: 'login',
  // 修改密码
  PASSWORD: 'password',
};

// *********************
// Hooks Function
// *********************

const state = reactive({
  showArea: SHOW_AREA_MAP.LOGIN, // 界面显示的区域
  isLogin: false, // 是否已经登录（登录过后选学校）
  loginText: '登 录', // 按钮文案
  loadingBtn: false, // 按钮加载状态
  accountFrom: {},
});

// 登录状态更新
const updateLoginStatus = status => {
  switch (status) {
    case 'success': {
      state.loginText = '登录';
      state.loadingBtn = false;
      break;
    }
    case 'error': {
      state.loginText = '登录失败';
      state.loadingBtn = false;
      store.clearUser();
      setTimeout(() => {
        state.loginText = '登录';
      }, 2000);
      break;
    }
    case 'loading': {
      state.loginText = '登录中...';
      state.loadingBtn = true;
      break;
    }
  }
};

// 获取用户信息
async function getCurrentUser() {
  const res = await http.post('/manage/me');
  if (res.code !== 0) {
    throw new Error(res.message);
  }
  store.userInfo = res.data;
  return res.data;
}

function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// 登录事件
async function submitLoginClick(accountFrom, { ticket, randStr }) {
  state.accountFrom = accountFrom;
  try {
    state.showArea = SHOW_AREA_MAP.LOGIN;
    updateLoginStatus('loading');
    const passWadData = {
      encoded: RSA.encrypt(JSON.stringify(accountFrom)),
      ...(ticket && { ticket }),
      ...(randStr && { randStr }),
    };
    const res = await http.post('/manage/auth/token', passWadData);
    if (res.code !== 0) {
      updateLoginStatus('error');
      return;
    }
    const { accessToken, refreshToken } = res.data;
    store.setToken({ accessToken, refreshToken });
    await sleep(500);
    const user = await getCurrentUser();
    if (user.needEnableOrg) {
      // 需要激活
      agencyActivateRef.value.showModel(user.fsseOrg);
    } else if (user.isPasswordExpired) {
      // 密码过期需要重置密码
      state.showArea = SHOW_AREA_MAP.PASSWORD;
    } else {
      // 登录成功 尝试一下获取路由参数决定重定向到哪
      try {
        const resultUrl = getTargetUrlParams();
        console.log(resultUrl, 'resultUrl');

        if (resultUrl && resultUrl.redirect_uri) {
          window.location.href = resultUrl.redirect_uri;
        } else {
          window.location.href = '/';
        }
      } catch (error) {
        console.error('获取URL参数失败:', error);
        window.location.href = '/';
      }

      updateLoginStatus('success');
    }
  } catch (error) {
    console.error('error: ', error);
    updateLoginStatus('error');
  }
}

const submitLogin = async accountFrom => {
  // 在显示验证码前保存URL参数到 sessionStorage
  console.log('登录前URL:', window.location.href);
  const beforeCaptchaParams = getTargetUrlParams();
  console.log('登录前URL参数:', beforeCaptchaParams);

  // 如果有 redirect_uri 参数，保存到 sessionStorage
  if (beforeCaptchaParams?.redirect_uri) {
    sessionStorage.setItem('temp_redirect_uri', beforeCaptchaParams.redirect_uri);
    console.log('保存重定向参数到 sessionStorage:', beforeCaptchaParams.redirect_uri);
  }

  // 显示验证码
  const captchaResult = await showTencentCaptcha('*********', {
    bizState: { accountFrom: accountFrom }, // 可以传递业务参数
    userLanguage: 'zh-cn',
  });

  // 验证码完成后再次检查URL参数
  console.log('验证码后URL:', window.location.href);
  const afterCaptchaParams = getTargetUrlParams();
  console.log('验证码后URL参数:', afterCaptchaParams);
  console.log('captchaResult', captchaResult);

  // 用户关闭了验证码
  if (captchaResult.ret === 2) {
    return;
  }

  // 检查是否为容灾票据
  if (isDisasterTicket(captchaResult)) {
    console.warn('使用容灾票据');

    // 这里可以添加额外的安全措施，如短信验证码等
  } else if (isValidCaptchaResult(captchaResult)) {
    console.log('验证通过');
  } else {
    console.log('验证失败，请重试');
    return;
  }

  await submitLoginClick(accountFrom, {
    ticket: captchaResult.ticket,
    randStr: captchaResult.randstr,
  });
};

// 不激活取消登录
const cancelok = () => {
  updateLoginStatus('success');
};

// 激活直接帮他自动登录进去
const confirmok = () => {
  window.location.href = '/';
};

// 组件挂载时检查是否已登录且有重定向参数
onMounted(() => {
  console.log('登录组件 onMounted - 当前URL:', window.location.href);
  console.log('登录组件 onMounted - accessToken:', !!store.accessToken);

  // 如果用户已经登录，检查是否有 redirect_uri 参数
  if (store.accessToken) {
    try {
      const urlParams = getTargetUrlParams();
      console.log('登录组件 onMounted - URL参数:', urlParams);
      const redirectUri = urlParams?.redirect_uri;

      if (redirectUri) {
        console.log('登录组件 onMounted - 有重定向参数，跳转到:', redirectUri);
        // 如果有 redirect_uri 参数，直接跳转
        window.location.href = redirectUri;
      } else {
        console.log('登录组件 onMounted - 无重定向参数，跳转到首页');
        // 如果没有 redirect_uri 参数，跳转到首页
        window.location.href = '/';
      }
    } catch (error) {
      console.error('获取URL参数失败:', error);
      // 如果获取参数失败，跳转到首页
      window.location.href = '/';
    }
  } else {
    console.log('登录组件 onMounted - 用户未登录，显示登录表单');
  }
});
</script>

<style lang="less" scoped>
.login_box {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  width: 100vw;
  height: 100vh;
  background: #fafefc;
  background-size: 100% 100%;
  padding-left: 110px;
  .introduction {
    display: flex;
    align-items: center;
    img {
      width: 701px;
      height: 560px;
    }
  }
  .form_box {
    box-sizing: border-box;
    width: 480px;
    height: 560px;
    padding: 78px 30px 86px;
    background: #ffffff;
    box-shadow: 0px 0px 20px 0px rgba(215, 215, 215, 0.5);
    border-radius: 12px;
    position: absolute;
    top: 50%;
    left: 75%;
    transform: translate3d(-50%, -50%, 0);
  }
  .copyright {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 14px;
    font-weight: 400;
    color: rgba(102, 102, 102, 0.88);
  }
}
</style>
